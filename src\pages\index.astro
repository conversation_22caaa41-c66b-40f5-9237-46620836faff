---
import BaseLayout from '../layouts/BaseLayout.astro';
---

<BaseLayout
  title="<PERSON> — Software Engineer & CTO"
  description="<PERSON> — Software Engineer, CTO at GitKraken, and creator of GitLens. Building exceptional digital experiences and developer tools."
>
  <a href="#main" class="skip-link">Skip to main content</a>

  <!-- Header -->
  <header class="header">
    <div class="header__content">
      <button class="header__brand" aria-label="Eric Am<PERSON>io">
        <img alt="<PERSON>" class="brand-image brand-image--primary" src="/ea-logo.svg" />
        <img alt="Eric Am<PERSON>io" class="brand-image brand-image--secondary" src="/ea-logo-alt.svg" />
      </button>

      <nav class="header__nav" aria-label="Main navigation">
        <a href="#about" class="header__nav-link">
          <span class="nav-number">01.</span>
          <span class="nav-text">About</span>
        </a>
        <a href="#gitlens" class="header__nav-link">
          <span class="nav-number">02.</span>
          <span class="nav-text">GitLens</span>
        </a>
        <a href="#projects" class="header__nav-link">
          <span class="nav-number">03.</span>
          <span class="nav-text">Work</span>
        </a>
        <a href="#contact" class="header__nav-link">
          <span class="nav-number">04.</span>
          <span class="nav-text">Contact</span>
        </a>
      </nav>

      <button class="theme-toggle" aria-label="Toggle dark mode">
        <img class="theme-icon theme-icon--light" src="/sun-icon.svg" alt="" />
        <img class="theme-icon theme-icon--dark" src="/moon-icon.svg" alt="" />
      </button>
    </div>
  </header>

  <!-- Main Content -->
  <main class="main">
    <!-- Hero Section -->
    <section class="hero section">
      <div class="hero__content">
        <h1 class="hero__greeting">Hi, my name is</h1>
        <h2 class="hero__name">Eric Amodio.</h2>
        <h3 class="hero__title">
          <span class="typing-text">I build things for developers.</span>
        </h3>
        <p class="hero__description">
          I'm a software engineer specializing in developer tools and experiences.
          Currently, I'm the CTO at <a href="https://gitkraken.com" class="link">GitKraken</a>
          and the creator of <a href="https://gitlens.amod.io" class="link">GitLens</a>,
          one of the most popular VS Code extensions with over 25 million downloads.
        </p>

        <div class="hero__actions">
          <a href="#gitlens" class="btn btn--primary">Check out my work!</a>
        </div>
      </div>
    </section>

    <!-- GitLens Featured Section -->
    <section id="gitlens" class="section gitlens-section">
      <div class="section__content">
        <div class="gitlens-hero">
          <div class="gitlens-hero__content">
            <div class="gitlens-hero__badge">
              <span class="badge">Featured</span>
              <span class="badge">VS Code Extension</span>
            </div>
            <h2 class="gitlens-hero__title">GitLens</h2>
            <p class="gitlens-hero__subtitle">
              Supercharge Visual Studio Code's Git capabilities
            </p>
            <div class="gitlens-hero__actions">
              <a href="https://marketplace.visualstudio.com/items?itemName=eamodio.gitlens"
                 class="btn btn--primary" target="_blank" rel="noopener">
                Install Extension
              </a>
              <a href="https://gitlens.amod.io"
                 class="btn btn--secondary" target="_blank" rel="noopener">
                Learn More
              </a>
              <a href="https://github.com/gitkraken/vscode-gitlens"
                 class="btn btn--secondary" target="_blank" rel="noopener">
                View Source
              </a>
            </div>
          </div>
        </div>

        <div class="gitlens-content">
          <div class="gitlens-content__text">
            <h3>About GitLens</h3>
            <p>
              GitLens is my most successful and widely-used creation. Originally built as a passion project
              to help developers better understand their codebase, GitLens has grown to become one of the
              most popular VS Code extensions with over 25 million downloads.
            </p>
            <p>
              The extension supercharges Visual Studio Code's Git capabilities by providing rich Git
              information directly within the editor. It helps developers understand code evolution,
              track changes, and collaborate more effectively.
            </p>
          </div>

          <div class="gitlens-stats">
            <h3>Impact & Statistics</h3>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">25M+</div>
                <div class="stat-label">Total Downloads</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">4.8/5</div>
                <div class="stat-label">Average Rating</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">2.5K+</div>
                <div class="stat-label">User Reviews</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">2016</div>
                <div class="stat-label">First Released</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="section about-section">
      <div class="section__content">
        <h2 class="section__title">About Me</h2>

        <div class="about__grid">
          <div class="about__text">
            <p>
              Hello! My name is Eric and I enjoy creating things that help developers be more productive.
              My interest in software development started back in the early 2000s when I decided
              to try building custom applications — turns out hacking together a custom app
              taught me a lot about software engineering!
            </p>
            <p>
              Fast-forward to today, and I've had the privilege of working at
              <a href="https://microsoft.com" class="link">Microsoft</a>,
              <a href="https://codestream.com" class="link">CodeStream</a>, and now
              <a href="https://gitkraken.com" class="link">GitKraken</a> as CTO.
              My main focus these days is building developer tools that enhance productivity and
              make complex workflows more accessible.
            </p>
            <p>
              I'm most known for creating <a href="https://gitlens.amod.io" class="link">GitLens</a>,
              a VS Code extension that supercharges Git capabilities and has been downloaded over 25 million times.
            </p>
            <p>Here are a few technologies I've been working with recently:</p>

            <ul class="tech-list">
              <li>TypeScript</li>
              <li>VS Code API</li>
              <li>Node.js</li>
              <li>React</li>
              <li>Electron</li>
              <li>Git</li>
            </ul>
          </div>

          <div class="about__image">
            <div class="image-wrapper">
              <div class="placeholder-image">EA</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="section projects-section">
      <div class="section__content">
        <h2 class="section__title">Some Things I've Built</h2>

        <div class="projects__grid">
          <!-- Featured Project: GitLens -->
          <div class="project-card project-card--featured">
            <div class="project-card__header">
              <div class="project-card__label">Featured Project</div>
              <h3>GitLens</h3>
              <div class="project-card__links">
                <a href="https://github.com/gitkraken/vscode-gitlens" target="_blank" rel="noopener" aria-label="GitHub">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z"/>
                  </svg>
                </a>
                <a href="https://marketplace.visualstudio.com/items?itemName=eamodio.gitlens" target="_blank" rel="noopener" aria-label="VS Code Marketplace">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"/>
                    <path d="M5 5a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2v-2a1 1 0 10-2 0v2H5V7h2a1 1 0 000-2H5z"/>
                  </svg>
                </a>
              </div>
            </div>
            <p class="project-card__description">
              Supercharge Visual Studio Code's Git capabilities. GitLens helps you better understand
              code through Git blame annotations, code lens, status bar details, file and line history,
              and more. Over 25 million downloads and 4.8/5 rating.
            </p>
            <ul class="project-card__tech">
              <li>TypeScript</li>
              <li>VS Code API</li>
              <li>Git</li>
              <li>Node.js</li>
              <li>Webpack</li>
            </ul>
          </div>

          <div class="project-card">
            <div class="project-card__header">
              <h3>GitKraken Tools</h3>
              <div class="project-card__links">
                <a href="https://gitkraken.com" target="_blank" rel="noopener" aria-label="GitKraken">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"/>
                    <path d="M5 5a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2v-2a1 1 0 10-2 0v2H5V7h2a1 1 0 000-2H5z"/>
                  </svg>
                </a>
              </div>
            </div>
            <p class="project-card__description">
              Leading the technical vision for GitKraken's suite of Git tools including GitKraken Client,
              CLI, and Browser Extension. Serving millions of developers worldwide.
            </p>
            <ul class="project-card__tech">
              <li>TypeScript</li>
              <li>Electron</li>
              <li>React</li>
              <li>Node.js</li>
              <li>Git</li>
            </ul>
          </div>

          <div class="project-card">
            <div class="project-card__header">
              <h3>VS Code Themes</h3>
              <div class="project-card__links">
                <a href="https://marketplace.visualstudio.com/search?term=eamodio&target=VSCode&category=Themes" target="_blank" rel="noopener" aria-label="VS Code Marketplace">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"/>
                    <path d="M5 5a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2v-2a1 1 0 10-2 0v2H5V7h2a1 1 0 000-2H5z"/>
                  </svg>
                </a>
              </div>
            </div>
            <p class="project-card__description">
              A collection of carefully crafted VS Code themes designed for optimal readability
              and beautiful syntax highlighting. Over 100K downloads across multiple themes.
            </p>
            <ul class="project-card__tech">
              <li>JSON</li>
              <li>Color Theory</li>
              <li>VS Code</li>
              <li>Design Systems</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section contact-section">
      <div class="section__content">
        <h2 class="section__title">Get In Touch</h2>
        <p class="contact__description">
          I'm always open to discussing new opportunities, interesting projects, or just having a chat about technology.
          Feel free to reach out if you'd like to connect!
        </p>

        <div class="contact__actions">
          <a href="mailto:<EMAIL>" class="btn btn--primary">Say Hello</a>
        </div>

        <div class="social-links">
          <a href="https://github.com/eamodio" target="_blank" rel="noopener" aria-label="GitHub">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
          </a>
          <a href="https://twitter.com/eamodio" target="_blank" rel="noopener" aria-label="Twitter">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
          </a>
          <a href="https://linkedin.com/in/eamodio" target="_blank" rel="noopener" aria-label="LinkedIn">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
            </svg>
          </a>
        </div>
      </div>
    </section>
  </main>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // Remove preload class to enable transitions
      setTimeout(() => {
        document.body.classList.remove('preload');
      }, 100);

      // Smooth scrolling for navigation links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            const headerHeight = 80;
            const targetPosition = target.offsetTop - headerHeight;

            window.scrollTo({
              top: targetPosition,
              behavior: 'smooth'
            });
          }
        });
      });

      // Header scroll effect
      const header = document.querySelector('.header');
      let lastScrollY = window.scrollY;

      window.addEventListener('scroll', () => {
        const scrollY = window.scrollY;

        if (scrollY > 100) {
          header.classList.add('scrolled');
        } else {
          header.classList.remove('scrolled');
        }

        lastScrollY = scrollY;
      });

      // Intersection Observer for animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
          }
        });
      }, observerOptions);

      // Observe elements for fade-in animation
      document.querySelectorAll('.fade-in').forEach((el) => {
        observer.observe(el);
      });

      // Active navigation link
      const navLinks = document.querySelectorAll('.header__nav-link');
      const sections = document.querySelectorAll('section[id]');

      const navObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const id = entry.target.getAttribute('id');
            navLinks.forEach(link => {
              link.classList.remove('active');
              if (link.getAttribute('href') === `#${id}`) {
                link.classList.add('active');
              }
            });
          }
        });
      }, {
        threshold: 0.3,
        rootMargin: '-80px 0px -60% 0px'
      });

      sections.forEach(section => {
        navObserver.observe(section);
      });

      // Theme toggle functionality
      const themeToggle = document.querySelector('.theme-toggle');
      const html = document.documentElement;

      // Update theme icon visibility
      function updateThemeIcon(theme) {
        const lightIcon = document.querySelector('.theme-icon--light');
        const darkIcon = document.querySelector('.theme-icon--dark');

        if (theme === 'dark') {
          if (lightIcon) lightIcon.style.display = 'none';
          if (darkIcon) darkIcon.style.display = 'block';
        } else {
          if (lightIcon) lightIcon.style.display = 'block';
          if (darkIcon) darkIcon.style.display = 'none';
        }
      }

      // Get initial theme
      const savedTheme = localStorage.getItem('theme');
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const initialTheme = savedTheme || (prefersDark ? 'dark' : 'light');

      // Set initial theme
      html.setAttribute('data-theme', initialTheme);
      updateThemeIcon(initialTheme);

      // Theme toggle click handler
      themeToggle?.addEventListener('click', () => {
        const currentTheme = html.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        html.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateThemeIcon(newTheme);
      });

      // Listen for system theme changes
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          const newTheme = e.matches ? 'dark' : 'light';
          html.setAttribute('data-theme', newTheme);
          updateThemeIcon(newTheme);
        }
      });

      // Simple typing effect for hero title
      const typingElement = document.getElementById('typing-text');
      if (typingElement) {
        const texts = [
          'Software Engineer & CTO',
          'GitLens Creator',
          'Open Source Enthusiast',
          'Technology Leader'
        ];
        let currentIndex = 0;
        let currentText = '';
        let isDeleting = false;
        let typeSpeed = 100;

        function type() {
          const fullText = texts[currentIndex];

          if (isDeleting) {
            currentText = fullText.substring(0, currentText.length - 1);
          } else {
            currentText = fullText.substring(0, currentText.length + 1);
          }

          typingElement.textContent = currentText;

          if (!isDeleting && currentText === fullText) {
            typeSpeed = 2000;
            isDeleting = true;
          } else if (isDeleting && currentText === '') {
            isDeleting = false;
            currentIndex = (currentIndex + 1) % texts.length;
            typeSpeed = 500;
          } else {
            typeSpeed = isDeleting ? 50 : 100;
          }

          setTimeout(type, typeSpeed);
        }

        // Start typing effect after a short delay
        setTimeout(type, 1000);
      }
    });
  </script>
</BaseLayout>

<style>
  /* Header */
  .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: transparent;
    transition: all 0.3s ease;
    padding: 1rem 0;
  }

  .header.scrolled {
    background: rgba(var(--base-background-rgb), 0.85);
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    border-bottom: 1px solid color-mix(in srgb, var(--base-accent) 15%, transparent);
    box-shadow: 0 8px 32px color-mix(in srgb, var(--base-accent) 8%, transparent);
  }

  .header__content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
  }

  .header__brand {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    position: relative;
    width: 48px;
    height: 48px;
    transition: transform 0.2s ease;
  }

  .header__brand:hover {
    transform: scale(1.05);
  }

  .brand-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: opacity 0.3s ease;
  }

  .brand-image--secondary {
    opacity: 0;
  }

  .header__brand:hover .brand-image--primary {
    opacity: 0;
  }

  .header__brand:hover .brand-image--secondary {
    opacity: 1;
  }

  .header__nav {
    display: flex;
    gap: 2.5rem;
    align-items: center;
  }

  .header__nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: var(--font-mono);
  }

  .nav-number {
    color: var(--accent-primary);
    font-size: 0.8rem;
    font-weight: 600;
    letter-spacing: 0.05em;
  }

  .nav-text {
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .header__nav-link:hover .nav-text,
  .header__nav-link.active .nav-text {
    color: var(--accent-primary);
    transform: translateY(-1px);
  }

  .header__nav-link:hover .nav-number,
  .header__nav-link.active .nav-number {
    color: var(--accent-secondary);
  }

  /* Underline effect */
  .header__nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--accent-primary);
    transition: width 0.3s ease;
  }

  .header__nav-link:hover::after,
  .header__nav-link.active::after {
    width: 100%;
  }

  .theme-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    position: relative;
    width: 40px;
    height: 40px;
    transition: background-color 0.2s ease;
  }

  .theme-toggle:hover {
    background: color-mix(in srgb, var(--base-accent) 10%, transparent);
  }

  .theme-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    color: var(--text-secondary);
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  .theme-icon--dark {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(180deg);
  }

  [data-theme="dark"] .theme-icon--light {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(-180deg);
  }

  [data-theme="dark"] .theme-icon--dark {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(0deg);
  }

  /* Main Layout */
  .main {
    padding-top: 80px;
  }

  .section {
    padding: var(--spacing-3xl) 0;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }

  .section__content {
    max-width: 1000px;
  }

  .section__title {
    font-size: var(--font-size-3xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xl);
    position: relative;
    display: flex;
    align-items: center;
  }

  .section__title::before {
    content: '01.';
    color: var(--accent-primary);
    font-family: var(--font-family-mono);
    font-size: var(--font-size-xl);
    margin-right: var(--spacing-md);
  }

  .about-section .section__title::before { content: '01.'; }
  .projects-section .section__title::before { content: '02.'; }
  .contact-section .section__title::before { content: '03.'; }

  .section__title::after {
    content: '';
    flex: 1;
    height: 1px;
    background: var(--border-color);
    margin-left: var(--spacing-lg);
  }

  /* Hero Section */
  .hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: var(--spacing-3xl) 0;
  }

  .hero__content {
    max-width: 1000px;
  }

  .hero__greeting {
    color: var(--accent-primary);
    font-family: var(--font-family-mono);
    font-size: var(--font-size-lg);
    font-weight: 400;
    margin: 0 0 var(--spacing-lg) 0;
  }

  .hero__name {
    font-size: clamp(40px, 8vw, 80px);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.1;
  }

  .hero__title {
    font-size: clamp(40px, 8vw, 60px);
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-lg) 0;
    line-height: 1.1;
  }

  .hero__description {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 540px;
    margin: 0 0 var(--spacing-2xl) 0;
  }

  .hero__actions {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
  }

  /* Buttons */
  .btn {
    display: inline-block;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius);
    text-decoration: none;
    font-family: var(--font-family-mono);
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
    border: 1px solid transparent;
  }

  .btn--primary {
    background: transparent;
    border-color: var(--accent-primary);
    color: var(--accent-primary);
  }

  .btn--primary:hover {
    background: rgba(145, 77, 179, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px -10px rgba(145, 77, 179, 0.3);
  }

  .btn--secondary {
    background: transparent;
    border-color: var(--border-color);
    color: var(--text-secondary);
  }

  .btn--secondary:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
    transform: translateY(-2px);
  }

  /* Links */
  .link {
    color: var(--accent-primary);
    text-decoration: none;
    position: relative;
    transition: var(--transition);
  }

  .link:hover {
    color: var(--accent-secondary);
  }

  .link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--accent-primary);
    transition: width 0.3s ease;
  }

  .link:hover::after {
    width: 100%;
  }

  /* About Section */
  .about__grid {
    display: grid;
    grid-template-columns: 3fr 2fr;
    gap: var(--spacing-2xl);
    align-items: start;
  }

  .about__text {
    font-size: var(--font-size-lg);
    line-height: 1.6;
  }

  .about__text p {
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
  }

  .tech-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
    list-style: none;
    padding: 0;
    margin: var(--spacing-lg) 0 0 0;
  }

  .tech-list li {
    position: relative;
    padding-left: var(--spacing-lg);
    font-family: var(--font-family-mono);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
  }

  .tech-list li::before {
    content: '▹';
    position: absolute;
    left: 0;
    color: var(--accent-primary);
    font-size: var(--font-size-base);
  }

  .about__image {
    position: relative;
    max-width: 300px;
    margin: 0 auto;
  }

  .image-wrapper {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
  }

  .image-wrapper:hover {
    transform: translate(-8px, -8px);
  }

  .image-wrapper::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    width: 100%;
    height: 100%;
    border: 2px solid var(--accent-primary);
    border-radius: var(--border-radius);
    z-index: -1;
    transition: var(--transition);
  }

  .image-wrapper:hover::after {
    top: 28px;
    left: 28px;
  }

  .placeholder-image {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: 700;
    color: white;
    border-radius: var(--border-radius);
  }

  /* Projects Section */
  .projects__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
  }

  .project-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    transition: var(--transition);
    position: relative;
  }

  .project-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--accent-primary);
  }

  .project-card--featured {
    border: 2px solid var(--accent-primary);
    background: linear-gradient(135deg, var(--surface) 0%, var(--background-tertiary) 100%);
  }

  .project-card__label {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-xs);
    color: var(--accent-primary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: var(--spacing-sm);
  }

  .project-card__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
  }

  .project-card__header h3 {
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    margin: 0;
  }

  .project-card__links {
    display: flex;
    gap: var(--spacing-sm);
  }

  .project-card__links a {
    color: var(--text-secondary);
    transition: var(--transition);
  }

  .project-card__links a:hover {
    color: var(--accent-primary);
  }

  .project-card__description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
  }

  .project-card__tech {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .project-card__tech li {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    background: var(--background-tertiary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
  }

  /* GitLens Section */
  .gitlens-section {
    background: linear-gradient(135deg, var(--surface) 0%, var(--background-tertiary) 100%);
    border: 1px solid var(--accent-primary);
    border-radius: var(--border-radius-lg);
    margin: var(--spacing-xl) 0;
  }

  .gitlens-hero {
    text-align: center;
    padding: var(--spacing-xl) 0;
    border-bottom: 1px solid var(--border-color);
  }

  .gitlens-hero__badge {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
  }

  .badge {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-xs);
    color: var(--accent-primary);
    background: rgba(145, 77, 179, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    text-transform: uppercase;
    letter-spacing: 0.1em;
  }

  .gitlens-hero__title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    color: var(--accent-primary);
    margin-bottom: var(--spacing-md);
    font-weight: 700;
  }

  .gitlens-hero__subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .gitlens-hero__actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
  }

  .gitlens-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
  }

  .gitlens-content h3 {
    color: var(--accent-primary);
    margin-bottom: var(--spacing-md);
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }

  .stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
  }

  .stat-number {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: var(--spacing-xs);
  }

  .stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
  }

  @media (max-width: 768px) {
    .gitlens-content {
      grid-template-columns: 1fr;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .gitlens-hero__actions {
      flex-direction: column;
      align-items: center;
    }
  }

  /* Contact Section */
  .contact-section {
    text-align: center;
  }

  .contact__description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto var(--spacing-2xl) auto;
    line-height: 1.6;
  }

  .contact__actions {
    margin-bottom: var(--spacing-2xl);
  }

  .social-links {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
  }

  .social-links a {
    color: var(--text-secondary);
    transition: var(--transition);
    padding: var(--spacing-sm);
  }

  .social-links a:hover {
    color: var(--accent-primary);
    transform: translateY(-2px);
  }

  /* Animations */
  .fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }

  .fade-in.visible {
    opacity: 1;
    transform: translateY(0);
  }

  /* Typing Animation */
  #typing-text::after {
    content: '|';
    color: var(--accent-primary);
    animation: blink 1s infinite;
  }

  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .header__nav {
      gap: 1.5rem;
    }

    .nav-text {
      font-size: 0.7rem;
    }
  }

  @media (max-width: 768px) {
    .header__nav {
      display: none;
    }

    .header__content {
      padding: 0 var(--spacing-md);
    }
  }

  @media (max-width: 480px) {
    .header__content {
      padding: 0 1rem;
    }

    .header__brand {
      width: 40px;
      height: 40px;
    }
  }

    .section {
      padding-left: var(--spacing-md);
      padding-right: var(--spacing-md);
    }

    .hero {
      padding: var(--spacing-2xl) 0;
    }

    .hero__actions {
      flex-direction: column;
      align-items: flex-start;
    }

    .about__grid {
      grid-template-columns: 1fr;
      gap: var(--spacing-xl);
    }

    .about__image {
      order: -1;
      max-width: 250px;
    }

    .tech-list {
      grid-template-columns: repeat(2, 1fr);
    }

    .projects__grid {
      grid-template-columns: 1fr;
    }

    .section__title::after {
      margin-left: var(--spacing-md);
    }
  }

  @media (max-width: 480px) {
    .hero__name {
      font-size: clamp(32px, 8vw, 60px);
    }

    .hero__title {
      font-size: clamp(28px, 8vw, 50px);
    }

    .hero__description {
      font-size: var(--font-size-base);
    }

    .section__title {
      font-size: var(--font-size-2xl);
    }

    .tech-list {
      grid-template-columns: 1fr;
    }

    .social-links {
      gap: var(--spacing-md);
    }
  }

  /* Utility Classes */
  .hidden {
    opacity: 0;
  }

  /* Animations */
  .fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
  }

  .fade-in.visible {
    opacity: 1;
    transform: translateY(0);
  }

  @media (prefers-reduced-motion: reduce) {
    .fade-in {
      opacity: 1;
      transform: none;
      transition: none;
    }
  }

</style>

<script>
  // Simple typing animation
  document.addEventListener('DOMContentLoaded', () => {
    const typingElement = document.getElementById('typing-text');
    if (typingElement) {
      const texts = [
        'I build things for developers.',
        'I create developer tools.',
        'I make Git more accessible.'
      ];
      let textIndex = 0;
      let charIndex = 0;
      let isDeleting = false;

      function typeText() {
        const currentText = texts[textIndex];

        if (isDeleting) {
          typingElement.textContent = currentText.substring(0, charIndex - 1);
          charIndex--;
        } else {
          typingElement.textContent = currentText.substring(0, charIndex + 1);
          charIndex++;
        }

        let typeSpeed = isDeleting ? 50 : 100;

        if (!isDeleting && charIndex === currentText.length) {
          typeSpeed = 2000; // Pause at end
          isDeleting = true;
        } else if (isDeleting && charIndex === 0) {
          isDeleting = false;
          textIndex = (textIndex + 1) % texts.length;
          typeSpeed = 500; // Pause before next text
        }

        setTimeout(typeText, typeSpeed);
      }

      typeText();
    }
  });
</script>
</BaseLayout>
