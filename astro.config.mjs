// @ts-check
import { defineConfig } from 'astro/config';

// https://astro.build/config
export default defineConfig({
  // GitHub Pages deployment configuration
  site: 'https://eamodio.github.io',
  base: '/website-auggie-astro',

  // Output configuration for static site generation
  output: 'static',

  // Build configuration
  build: {
    assets: 'assets',
    inlineStylesheets: 'auto',
  },

  // Vite configuration for development and build
  vite: {
    build: {
      rollupOptions: {
        output: {
          assetFileNames: 'assets/[name].[hash][extname]',
          chunkFileNames: 'assets/[name].[hash].js',
          entryFileNames: 'assets/[name].[hash].js'
        }
      }
    },
    css: {
      devSourcemap: true
    }
  },

  // Markdown configuration
  markdown: {
    shikiConfig: {
      theme: 'github-dark',
      wrap: true
    }
  }
});
