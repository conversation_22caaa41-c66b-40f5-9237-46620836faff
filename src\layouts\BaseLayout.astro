---
export interface Props {
  title: string;
  description?: string;
  image?: string;
  canonical?: string;
}

const { title, description = "<PERSON>-stack developer, entrepreneur, and open-source enthusiast", image, canonical } = Astro.props;

const canonicalURL = canonical || new URL(Astro.url.pathname, Astro.site);
const socialImage = image || new URL('/assets/social-image.jpg', Astro.site);
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href={`${import.meta.env.BASE_URL}favicon.svg`} />
    <meta name="generator" content={Astro.generator} />
    
    <!-- Canonical URL -->
    <link rel="canonical" href={canonicalURL} />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={socialImage} />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalURL} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={socialImage} />
    <meta property="twitter:creator" content="@eamodio" />
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet" />
    
    <!-- Theme color -->
    <meta name="theme-color" content="#914db3" />
    
    <title>{title}</title>
  </head>
  <body class="preload">
    <slot />

    <!-- Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-110925859-1"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'UA-110925859-1');
    </script>
  </body>
</html>

<style is:global>
  /* Minimal Base Color Palette - Easy Theme Management */
  :root {
    /* Base Colors - Only these need to change for new themes */
    --base-background: #120024;
    --base-text: #f2e8f7;
    --base-accent: #914db3;

    /* Derived Colors using color-mix() */
    --background-primary: var(--base-background);
    --background-secondary: color-mix(in srgb, var(--base-background) 85%, var(--base-accent) 15%);
    --background-tertiary: color-mix(in srgb, var(--base-background) 70%, var(--base-accent) 30%);
    --surface: color-mix(in srgb, var(--base-background) 60%, var(--base-accent) 40%);
    --surface-hover: color-mix(in srgb, var(--base-background) 50%, var(--base-accent) 50%);

    --text-primary: var(--base-text);
    --text-secondary: color-mix(in srgb, var(--base-text) 70%, var(--base-background) 30%);
    --text-muted: color-mix(in srgb, var(--base-text) 50%, var(--base-background) 50%);

    --accent-primary: var(--base-accent);
    --accent-secondary: color-mix(in srgb, var(--base-accent) 70%, var(--base-text) 30%);
    --accent-tertiary: color-mix(in srgb, var(--base-accent) 50%, var(--base-text) 50%);

    --border-color: color-mix(in srgb, var(--base-text) 30%, var(--base-background) 70%);
    --shadow-color: color-mix(in srgb, var(--base-accent) 25%, transparent 75%);

    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

    /* Font Sizes */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Layout */
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }

  /* Light Theme - Only change base colors, everything else derives automatically */
  [data-theme="light"] {
    --base-background: #fdfcff;
    --base-text: #090012;
    --base-accent: #914db3;
    /* All other colors automatically derived via color-mix() */
  }

  /* Dark Theme - Only change base colors, everything else derives automatically */
  [data-theme="dark"] {
    --base-background: #120024;
    --base-text: #f2e8f7;
    --base-accent: #914db3;
    /* All other colors automatically derived via color-mix() */
  }

  /*
  Example: Creating a new theme is now super simple!
  Just change these 3 base colors and everything else updates automatically:

  [data-theme="blue"] {
    --base-background: #001122;
    --base-text: #e8f4ff;
    --base-accent: #3b82f6;
  }

  [data-theme="green"] {
    --base-background: #0a1f0a;
    --base-text: #e8f5e8;
    --base-accent: #22c55e;
  }
  */

  /* CSS Reset and Base Styles */
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }

  html {
    font-family: var(--font-family-sans);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-secondary);
    height: 100%;
    width: 100%;
    scroll-behavior: smooth;
    box-sizing: border-box;
  }

  *,
  *::before,
  *::after {
    box-sizing: inherit;
  }

  body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, var(--background-tertiary) 100%);
    color: var(--text-secondary);
    position: relative;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background 0.3s ease, color 0.3s ease;
  }

  body.preload * {
    transition: none !important;
  }

  /* Selection styles */
  ::selection {
    background-color: var(--lightest-navy);
    color: var(--lightest-slate);
  }

  /* Focus styles for accessibility */
  :focus {
    outline: 2px dashed var(--green);
    outline-offset: 3px;
  }

  :focus:not(:focus-visible) {
    outline: none;
    outline-offset: 0px;
  }

  :focus-visible {
    outline: 2px dashed var(--green);
    outline-offset: 3px;
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Skip link for screen readers */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--green);
    color: var(--navy);
    padding: 8px;
    text-decoration: none;
    z-index: 1000;
    border-radius: var(--border-radius);
    font-family: var(--font-mono);
    font-size: var(--fz-xs);
    transition: var(--transition);
  }

  .skip-link:focus {
    top: 6px;
    outline: none;
    box-shadow: 4px 4px 0 0 var(--green);
    transform: translate(-5px, -5px);
  }

  /* Base typography */
  h1, h2, h3, h4, h5, h6 {
    margin: 0 0 10px 0;
    font-weight: 600;
    color: var(--lightest-slate);
    line-height: 1.1;
  }

  .big-heading {
    margin: 0;
    font-size: clamp(40px, 8vw, 80px);
  }

  .medium-heading {
    margin: 0;
    font-size: clamp(40px, 8vw, 60px);
  }

  p {
    margin: 0 0 15px 0;
  }

  p:last-child,
  p:last-of-type {
    margin: 0;
  }

  a {
    display: inline-block;
    text-decoration: none;
    text-decoration-skip-ink: auto;
    color: inherit;
    position: relative;
    transition: var(--transition);
  }

  a:hover,
  a:focus {
    color: var(--green);
  }

  /* Inline link styles */
  a.inline-link {
    display: inline-block;
    position: relative;
    color: var(--green);
    transition: var(--transition);
  }

  a.inline-link:hover,
  a.inline-link:focus-visible {
    color: var(--green);
    outline: 0;
  }

  a.inline-link:hover:after,
  a.inline-link:focus-visible:after {
    width: 100%;
  }

  a.inline-link:after {
    content: '';
    display: block;
    width: 0;
    height: 1px;
    position: relative;
    bottom: 0.37em;
    background-color: var(--green);
    opacity: 0.5;
    transition: var(--transition);
  }

  /* Utility classes */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .hidden {
    display: none;
  }

  .visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
  }
</style>
  </head>
  <body>
    <slot />

    <!-- Load EA Logo Web Component -->
    <script type="module">
      import '/src/components/EALogo.js';
    </script>
  </body>
</html>
