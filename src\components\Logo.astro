---
export interface Props {
  variant?: 'outline' | 'emboss';
  size?: number;
}

const { variant = 'outline', size = 48 } = Astro.props;
---

<!-- Simple EA text logo matching localhost:8080 style -->
<div class="ea-logo" style={`width: ${size}px; height: ${size}px;`}>
  <span class="ea-text">EA</span>
</div>

<script>
  class EALogo extends HTMLElement {
    constructor() {
      super();
    }
    
    connectedCallback() {
      this.style.display = 'inline-block';
      this.style.color = 'var(--accent-primary)';
      this.style.transition = 'var(--transition)';
      
      // Add hover effect
      this.addEventListener('mouseenter', () => {
        this.style.transform = 'scale(1.1)';
        this.style.color = 'var(--accent-secondary)';
      });
      
      this.addEventListener('mouseleave', () => {
        this.style.transform = 'scale(1)';
        this.style.color = 'var(--accent-primary)';
      });
    }
    
    setAttribute(name, value) {
      super.setAttribute(name, value);
      if (name === 'variant') {
        this.updateVariant(value);
      }
    }
    
    updateVariant(variant) {
      // This would update the SVG content based on variant
      // For now, we'll handle this in the Astro template
    }
  }
  
  if (!customElements.get('ea-logo')) {
    customElements.define('ea-logo', EALogo);
  }
</script>

<style>
  .ea-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    user-select: none;
    transition: var(--transition);
    border-radius: 8px;
    background: linear-gradient(135deg, #914db3, #ba7dd9);
    box-shadow: 0 4px 12px rgba(145, 77, 179, 0.3);
  }

  .ea-text {
    font-family: var(--font-family-mono);
    font-weight: 700;
    font-size: 1.2em;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .ea-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(145, 77, 179, 0.4);
  }
</style>
