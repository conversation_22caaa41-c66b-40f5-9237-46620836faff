// EA Logo Web Component - matches localhost:8080
class EALogo extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: 'open' });
  }

  static get observedAttributes() {
    return ['variant', 'size'];
  }

  connectedCallback() {
    this.render();
  }

  attributeChangedCallback() {
    this.render();
  }

  get variant() {
    return this.getAttribute('variant') || 'outline';
  }

  get size() {
    return this.getAttribute('size') || '48';
  }

  render() {
    const variant = this.variant;
    const size = this.size;

    this.shadowRoot.innerHTML = `
      <style>
        :host {
          display: inline-block;
          width: ${size}px;
          height: ${size}px;
          cursor: pointer;
          user-select: none;
          transition: transform 0.2s ease;
        }

        :host(:hover) {
          transform: scale(1.05);
        }

        svg {
          width: 100%;
          height: 100%;
          display: block;
        }

        .logo-gradient {
          --color-start: #914db3;
          --color-end: #ba7dd9;
        }

        .logo-emboss {
          filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        }
      </style>
      
      <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="logo-gradient-${this.id || 'default'}" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#914db3;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#ba7dd9;stop-opacity:1" />
          </linearGradient>
          ${variant === 'emboss' ? `
          <filter id="emboss-${this.id || 'default'}" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="1"/>
            <feSpecularLighting result="specOut" in="blur" specularConstant="1.5" specularExponent="20" lighting-color="white">
              <fePointLight x="-5000" y="-10000" z="20000"/>
            </feSpecularLighting>
            <feComposite in="specOut" in2="SourceAlpha" operator="in" result="specOut2"/>
            <feComposite in="SourceGraphic" in2="specOut2" operator="arithmetic" k1="0" k2="1" k3="1" k4="0"/>
          </filter>
          ` : ''}
        </defs>
        
        <!-- E -->
        <path d="M8 12h12v4H12v4h8v4h-8v4h12v4H8V12z" 
              fill="url(#logo-gradient-${this.id || 'default'})"
              ${variant === 'emboss' ? `filter="url(#emboss-${this.id || 'default'})"` : ''} />
        
        <!-- A -->
        <path d="M28 12h4l8 20h-4l-1.5-4h-9l-1.5 4h-4l8-20zm2 12h6l-3-8-3 8z" 
              fill="url(#logo-gradient-${this.id || 'default'})"
              ${variant === 'emboss' ? `filter="url(#emboss-${this.id || 'default'})"` : ''} />
      </svg>
    `;
  }
}

// Register the custom element
customElements.define('ea-logo', EALogo);

export default EALogo;
