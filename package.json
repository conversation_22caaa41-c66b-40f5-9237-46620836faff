{"name": "eric-amodio-website", "type": "module", "version": "1.0.0", "description": "<PERSON>'s personal website and blog - built with As<PERSON> and Lit", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://amod.io", "repository": {"type": "git", "url": "https://github.com/eamodio/eamodio.github.io.git"}, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro", "check": "astro check", "lint": "astro check", "deploy": "pnpm run build && gh-pages -d dist"}, "dependencies": {"@astrojs/lit": "^4.3.0", "@lit/reactive-element": "^2.1.1", "astro": "^5.13.7", "lit": "^3.3.1"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@types/node": "^24.5.0", "typescript": "^5.9.2"}, "keywords": ["astro", "lit", "personal-website", "portfolio", "typescript", "web-components"]}