---
export interface Props {
  currentPath?: string;
}

const { currentPath = '' } = Astro.props;

const navigation = [
  { name: 'About', href: '#about', action: 'about' },
  { name: 'Git<PERSON><PERSON>', href: '#gitlens', action: 'gitlens' },
  { name: 'Pure', href: '#pure', action: 'pure' },
  { name: 'Blog', href: '/blog', action: null },
  { name: 'Resume', href: '/resume', action: null }
];
---

<header class="header" role="banner">
  <h1 class="header__headline">
    <a href="/" aria-label="Eric Amodio - Home"><PERSON></a>
  </h1>
  
  <nav class="header__nav" role="navigation" aria-label="Main navigation">
    <ul class="header__buttons">
      {navigation.map(({ name, href, action }) => (
        <li>
          <a
            href={href}
            class="button button--flat"
            data-action={action}
            id={action ? `${action}-button` : undefined}
            aria-current={currentPath === href ? 'page' : undefined}
          >
            {name}
          </a>
        </li>
      ))}
    </ul>
  </nav>
</header>

<style>
  .header {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    background: transparent;
    color: var(--text-primary);
    padding: 0.5em;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1;
    flex-wrap: nowrap;
  }

  .header__headline {
    flex: auto 1 0;
    font-size: 2em;
    letter-spacing: 0.125em;
    margin: 0;
    max-width: 960px;
    text-shadow: 2px 3px 6px rgba(0, 0, 0, 0.8), 0 0 42px rgba(255, 255, 255, 0.1);
    transform: translate(5px, -300%);
    transform-origin: top left;
    transition: transform 0.5s cubic-bezier(1, 0.4, 0.7, 1.5);
    white-space: nowrap;
  }

  .header__headline a {
    color: inherit;
    text-decoration: none;
  }

  .header__headline a:hover,
  .header__headline a:focus {
    color: inherit;
  }

  :global(body.is-section) .header__headline {
    transform: translate(5px, 8%);
    transition-delay: 0.6s;
  }

  .header__nav {
    display: flex;
  }

  .header__buttons {
    display: flex;
    flex-wrap: nowrap;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5em;
  }

  .button {
    background: transparent;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.8em;
    letter-spacing: 0.25em;
    padding: 1em 1.75em;
    text-decoration: none;
    text-transform: uppercase;
    user-select: none;
    white-space: nowrap;
    display: inline-block;
    transition: var(--transition);
  }

  .button:focus {
    outline: 2px solid var(--accent);
    outline-offset: 2px;
  }

  .button--flat {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.6);
    color: var(--text-primary);
  }

  @supports (backdrop-filter: blur(10px)) {
    .button--flat {
      backdrop-filter: blur(10px);
      background: rgba(0, 0, 0, 0.2);
    }
  }

  .button--flat:hover,
  .button--flat:focus {
    background-color: var(--accent);
    color: var(--background);
    border-color: var(--accent);
  }

  /* Active state for current page */
  .button--flat[aria-current="page"] {
    background: var(--accent);
    color: var(--background);
    border-color: var(--accent);
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .header__nav-list {
      display: none;
    }

    .header__cta {
      margin-left: 0;
      padding-left: 0;
    }
  }

  @media (max-width: 480px) {
    .header {
      padding: 0 15px;
    }

    .resume-button {
      padding: 0.5rem 0.75rem;
      font-size: var(--fz-xxs);
    }
  }
</style>
