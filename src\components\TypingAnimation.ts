import { LitElement, html, css, PropertyValues } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';

@customElement('typing-animation')
export class TypingAnimation extends LitElement {
  @property({ type: Array }) strings: string[] = [];
  @property({ type: Number }) typeSpeed = 90;
  @property({ type: Number }) backSpeed = 30;
  @property({ type: Number }) backDelay = 1500;
  @property({ type: Boolean }) loop = true;
  @property({ type: Boolean }) showCursor = true;
  @property({ type: String }) cursorChar = '|';
  @property({ type: Boolean }) autoStart = true;

  @state() private currentText = '';
  @state() private currentStringIndex = 0;
  @state() private isTyping = true;
  @state() private isComplete = false;
  @state() private showCursorBlink = true;

  private timeoutId?: number;
  private currentCharIndex = 0;

  static styles = css`
    :host {
      display: inline-block;
      font-family: inherit;
    }

    .typing-container {
      position: relative;
    }

    .cursor {
      animation: blink 1s infinite;
      opacity: 1;
    }

    .cursor.hidden {
      opacity: 0;
    }

    @keyframes blink {
      0%, 50% { opacity: 1; }
      51%, 100% { opacity: 0; }
    }

    @media (prefers-reduced-motion: reduce) {
      .cursor {
        animation: none;
        opacity: 1;
      }
    }
  `;

  protected firstUpdated(changedProperties: PropertyValues): void {
    super.firstUpdated(changedProperties);
    if (this.autoStart && this.strings.length > 0) {
      this.start();
    }
  }

  disconnectedCallback(): void {
    super.disconnectedCallback();
    this.stop();
  }

  start(): void {
    if (this.strings.length === 0) return;
    this.currentStringIndex = 0;
    this.currentCharIndex = 0;
    this.currentText = '';
    this.isTyping = true;
    this.isComplete = false;
    this.typeString();
  }

  stop(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = undefined;
    }
  }

  pause(): void {
    this.stop();
  }

  resume(): void {
    if (!this.isComplete) {
      if (this.isTyping) {
        this.typeString();
      } else {
        this.backspaceString();
      }
    }
  }

  private typeString(): void {
    const currentString = this.strings[this.currentStringIndex];
    if (!currentString) return;

    this.showCursorBlink = false;
    
    if (this.currentCharIndex < currentString.length) {
      this.currentText = currentString.substring(0, this.currentCharIndex + 1);
      this.currentCharIndex++;
      
      this.timeoutId = window.setTimeout(() => {
        this.typeString();
      }, this.typeSpeed + this.humanize(this.typeSpeed));
    } else {
      // Finished typing current string
      this.showCursorBlink = true;
      this.isTyping = false;
      
      // Check if this is the last string and we're not looping
      if (this.currentStringIndex === this.strings.length - 1 && !this.loop) {
        this.isComplete = true;
        this.dispatchEvent(new CustomEvent('typing-complete', {
          detail: { text: this.currentText, stringIndex: this.currentStringIndex }
        }));
        return;
      }
      
      this.timeoutId = window.setTimeout(() => {
        this.backspaceString();
      }, this.backDelay);
    }
  }

  private backspaceString(): void {
    this.showCursorBlink = false;
    
    if (this.currentCharIndex > 0) {
      this.currentCharIndex--;
      this.currentText = this.strings[this.currentStringIndex].substring(0, this.currentCharIndex);
      
      this.timeoutId = window.setTimeout(() => {
        this.backspaceString();
      }, this.backSpeed + this.humanize(this.backSpeed));
    } else {
      // Finished backspacing, move to next string
      this.isTyping = true;
      this.currentStringIndex = (this.currentStringIndex + 1) % this.strings.length;
      
      this.timeoutId = window.setTimeout(() => {
        this.typeString();
      }, this.typeSpeed);
    }
  }

  private humanize(speed: number): number {
    return Math.round(Math.random() * speed / 2);
  }

  render() {
    return html`
      <span class="typing-container">
        <span class="text">${this.currentText}</span>
        ${this.showCursor ? html`
          <span class="cursor ${this.showCursorBlink ? '' : 'hidden'}">${this.cursorChar}</span>
        ` : ''}
      </span>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'typing-animation': TypingAnimation;
  }
}
