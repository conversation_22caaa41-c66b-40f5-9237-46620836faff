{"extends": "astro/tsconfigs/strict", "compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "strict": true, "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "useDefineForClassFields": false, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/layouts/*": ["src/layouts/*"], "@/pages/*": ["src/pages/*"], "@/assets/*": ["public/assets/*"]}}, "include": [".astro/types.d.ts", "src/**/*", "astro.config.mjs"], "exclude": ["node_modules", "dist"]}