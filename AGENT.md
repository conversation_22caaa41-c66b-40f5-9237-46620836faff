# AI Agent Guide for <PERSON>'s Personal Website

## Project Overview

This is <PERSON>'s personal website and blog built with **Astro** and **Lit** web components.

### Key Information

- **Owner**: <PERSON> (@eamodio) - Software Engineer, CTO at GitKraken, creator of GitLens
- **Framework**: Astro v5.13.7+ with static site generation
- **Components**: Lit web components for interactive elements
- **Deployment**: GitHub Pages (https://eamodio.github.io/eamodio.github.io/)
- **Production URLs**: https://amod.io
- **Local Dev**: http://localhost:4321 (Astro default)

## Project Structure

```
/
├── public/                 # Static assets (logos, icons, images)
├── src/
│   ├── components/        # Reusable components
│   ├── layouts/
│   │   └── BaseLayout.astro # Main layout template
│   └── pages/
│       └── index.astro    # Homepage
├── astro.config.mjs       # Astro configuration
├── package.json           # Dependencies and scripts
└── tsconfig.json          # TypeScript configuration
```

## Technology Stack & Dependencies

### Core Framework

- **Astro**: v5.13.7+ - Static site generator with component islands
- **Lit**: v3.3.1+ - Web components library for interactive elements
- **TypeScript**: Strict mode enabled for type safety

### Key Features

- **Static Site Generation**: Optimized for GitHub Pages deployment
- **Component Islands**: Hydration only where needed for performance
- **Web Standards**: Modern CSS with color-mix(), CSS custom properties
- **Accessibility**: WCAG compliance, semantic HTML, ARIA attributes
- **Theme System**: Dark/light mode with system preference detection

## Development Guidelines

### Code Style & Standards

1. **Accessibility First**: Always include proper ARIA labels, semantic HTML, and keyboard navigation
2. **Performance**: Use Astro's static generation, minimize JavaScript, optimize images
3. **Modern CSS**: Leverage CSS custom properties, color-mix(), and modern layout techniques
4. **TypeScript**: Use strict typing for all components and utilities
5. **Web Components**: Use Lit for interactive components that need client-side behavior

### Component Architecture

- **Astro Components**: For static content and layouts (.astro files)
- **Lit Components**: For interactive elements requiring client-side JavaScript (.js/.ts files)
- **Styling**: Component-scoped CSS with global design tokens in BaseLayout.astro

### Design System

The project uses a minimal color palette system with CSS custom properties:

```css
:root {
  /* Base Colors - Primary theme variables */
  --base-background: #120024; /* Deep purple background */
  --base-text: #f2e8f7; /* Light purple text */
  --base-accent: #914db3; /* Purple accent */

  /* Derived colors using color-mix() for consistency */
  --background-primary: var(--base-background);
  --background-secondary: color-mix(
    in srgb,
    var(--base-background) 85%,
    var(--base-accent) 15%
  );
  /* ... additional derived colors */
}
```

## Development Commands

```bash
# Development
npm run dev          # Start dev server at localhost:4321
npm run start        # Alias for dev

# Building & Testing
npm run build        # Build for production (includes type checking)
npm run preview      # Preview production build locally
npm run check        # Run Astro type checking
npm run lint         # Alias for check

# Deployment
npm run deploy       # Build and deploy to GitHub Pages
```

## Content Migration Strategy

### Current Content Sources

- **Main Site**: https://amod.io - Professional portfolio, about, projects
- **Blog**: https://blog.amod.io - Technical articles, thoughts, updates
- **Target**: Single consolidated Astro site with all content

### SEO & URL Preservation

- Maintain existing URL structure where possible
- Implement proper redirects for changed URLs
- Preserve meta tags, structured data, and social media cards
- Ensure Core Web Vitals compliance

## Accessibility Requirements

### WCAG Compliance

- **Level AA compliance** minimum
- Semantic HTML structure with proper heading hierarchy
- ARIA labels and descriptions for interactive elements
- Keyboard navigation support for all interactive components
- Color contrast ratios meeting WCAG standards
- Screen reader compatibility

### Implementation Examples

```astro
<!-- Proper semantic structure -->
<header role="banner">
  <nav aria-label="Main navigation">
    <a href="#main" class="skip-link">Skip to main content</a>
  </nav>
</header>

<main id="main" role="main">
  <!-- Main content -->
</main>
```

## Performance Optimization

### Astro Best Practices

- Use static generation for all possible content
- Minimize client-side JavaScript with component islands
- Optimize images with proper formats and sizes
- Implement proper caching headers for GitHub Pages

### Lit Component Guidelines

- Use `@customElement` decorator for proper registration
- Implement proper lifecycle methods (connectedCallback, disconnectedCallback)
- Use shadow DOM for style encapsulation when appropriate
- Minimize bundle size with tree-shaking

## GitHub Pages Deployment

### Configuration

The site is configured for GitHub Pages deployment with:

- **Base URL**: `/` (repository name)
- **Site URL**: `https://amod.io`
- **Output**: Static files in `dist/` directory
- **Build Process**: Automated via GitHub Actions (if configured)

### Asset Handling

- All assets use relative paths compatible with base URL
- SVG icons and logos are optimized for web delivery
- Proper cache headers for static assets

## Common Tasks & Patterns

### Adding New Pages

1. Create `.astro` file in `src/pages/`
2. Use `BaseLayout` for consistent structure
3. Add navigation links if needed
4. Ensure proper meta tags and accessibility

### Creating Interactive Components

1. Use Lit for client-side interactivity
2. Register with `@customElement` decorator
3. Implement proper TypeScript types
4. Add to component library in `src/components/`

### Theme System Usage

The theme toggle system supports:

- System preference detection
- Local storage persistence
- Smooth transitions between themes
- Proper ARIA labeling for accessibility

## Troubleshooting

### Common Issues

1. **Build Failures**: Check TypeScript errors with `npm run check`
2. **Asset Loading**: Verify paths are relative to base URL
3. **Component Hydration**: Ensure Lit components are properly imported
4. **GitHub Pages**: Check base URL configuration in astro.config.mjs

### Debug Commands

```bash
npm run check        # Type checking
npm run build        # Full build with error reporting
npm run preview      # Test production build locally
```

---

**Note for AI Assistants**: Always prioritize accessibility, performance, and web standards when making suggestions or modifications. Respect the existing design system and component architecture. When in doubt, ask for clarification rather than making assumptions about design or functionality requirements.
