---
// Theme toggle component
---

<button class="theme-toggle" aria-label="Toggle theme" data-theme-toggle>
  <svg class="theme-icon theme-icon--light" width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
    <!-- Sun icon -->
    <path d="M10 2L13.09 8.26L20 9L14 14.74L15.18 21.02L10 17.77L4.82 21.02L6 14.74L0 9L6.91 8.26L10 2Z"/>
  </svg>
  
  <svg class="theme-icon theme-icon--dark" width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
    <!-- Moon icon -->
    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"/>
  </svg>
</button>

<script>
  class ThemeToggle {
    constructor() {
      this.button = document.querySelector('[data-theme-toggle]');
      this.init();
    }
    
    init() {
      // Get initial theme
      const savedTheme = localStorage.getItem('theme');
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const initialTheme = savedTheme || (prefersDark ? 'dark' : 'light');
      
      this.setTheme(initialTheme);
      
      // Add click listener
      this.button?.addEventListener('click', () => {
        this.toggleTheme();
      });
      
      // Listen for system theme changes
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          this.setTheme(e.matches ? 'dark' : 'light');
        }
      });
    }
    
    toggleTheme() {
      const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
      const newTheme = currentTheme === 'light' ? 'dark' : 'light';
      this.setTheme(newTheme);
      localStorage.setItem('theme', newTheme);
    }
    
    setTheme(theme) {
      document.documentElement.setAttribute('data-theme', theme);
      this.updateIcon(theme);
    }
    
    updateIcon(theme) {
      const lightIcon = this.button?.querySelector('.theme-icon--light');
      const darkIcon = this.button?.querySelector('.theme-icon--dark');
      
      if (theme === 'dark') {
        lightIcon?.style.setProperty('display', 'none');
        darkIcon?.style.setProperty('display', 'block');
      } else {
        lightIcon?.style.setProperty('display', 'block');
        darkIcon?.style.setProperty('display', 'none');
      }
    }
  }
  
  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new ThemeToggle();
  });
</script>

<style>
  .theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    position: relative;
  }

  .theme-toggle:hover {
    color: var(--accent-primary);
    border-color: var(--accent-primary);
    background: var(--surface);
  }

  .theme-toggle:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
  }

  .theme-icon {
    position: absolute;
    transition: var(--transition);
  }

  .theme-icon--dark {
    display: none;
  }

  /* Dark theme styles */
  [data-theme="dark"] .theme-icon--light {
    display: none;
  }

  [data-theme="dark"] .theme-icon--dark {
    display: block;
  }
</style>
